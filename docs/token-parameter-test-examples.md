# Token 参数测试示例

## 商品回调通知接口测试

### HTTP 请求示例

```http
POST /api/open/callback/virtual/goods/notify/abc123456 HTTP/1.1
Host: api.example.com
Content-Type: application/json

[
  {
    "goodsNo": "G001",
    "goodsType": 1,
    "price": "100",
    "stock": 50,
    "status": 1,
    "changeTime": "1640995200"
  },
  {
    "goodsNo": "G002", 
    "goodsType": 1,
    "price": "200",
    "stock": 30,
    "status": 1,
    "changeTime": "1640995200"
  }
]
```

### 映射到 gRPC 请求

```protobuf
GoodsCallbackRequest {
  token: "abc123456"
  items: [
    {
      goods_no: "G001"
      goods_type: 1
      price: 100
      stock: 50
      status: 1
      change_time: 1640995200
    },
    {
      goods_no: "G002"
      goods_type: 1
      price: 200
      stock: 30
      status: 1
      change_time: 1640995200
    }
  ]
}
```

### cURL 测试命令

```bash
curl -X POST \
  'http://localhost:7788/api/open/callback/virtual/goods/notify/abc123456' \
  -H 'Content-Type: application/json' \
  -d '[
    {
      "goodsNo": "G001",
      "goodsType": 1,
      "price": "100",
      "stock": 50,
      "status": 1,
      "changeTime": "1640995200"
    }
  ]'
```

## 订单回调通知接口测试

### HTTP 请求示例

```http
POST /api/open/callback/virtual/order/notify/xyz789012 HTTP/1.1
Host: api.example.com
Content-Type: application/json

{
  "orderType": 1,
  "orderNo": "O001",
  "outOrderNo": "OUT001",
  "orderStatus": 2,
  "endTime": "1640995200",
  "cardItems": [
    {
      "cardNo": "CARD001",
      "cardPwd": "PWD001"
    }
  ],
  "remark": "订单完成"
}
```

### 映射到 gRPC 请求

```protobuf
OrderCallbackRequest {
  token: "xyz789012"
  order_type: 1
  order_no: "O001"
  out_order_no: "OUT001"
  order_status: 2
  end_time: 1640995200
  card_items: [
    {
      card_no: "CARD001"
      card_pwd: "PWD001"
    }
  ]
  remark: "订单完成"
}
```

### cURL 测试命令

```bash
curl -X POST \
  'http://localhost:7788/api/open/callback/virtual/order/notify/xyz789012' \
  -H 'Content-Type: application/json' \
  -d '{
    "orderType": 1,
    "orderNo": "O001",
    "outOrderNo": "OUT001",
    "orderStatus": 2,
    "endTime": "1640995200",
    "cardItems": [
      {
        "cardNo": "CARD001",
        "cardPwd": "PWD001"
      }
    ],
    "remark": "订单完成"
  }'
```

## 验证令牌接口测试

### HTTP 请求示例（如果添加了HTTP路由）

```http
POST /admin/v1/validate_token HTTP/1.1
Host: api.example.com
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "token": "abc123456",
  "clientType": "admin"
}
```

### 映射到 gRPC 请求

```protobuf
ValidateTokenRequest {
  token: "abc123456"
  client_type: ADMIN
}
```

## 测试验证要点

### 1. 路径参数绑定
- 确认 token 参数正确从 URL 路径中提取
- 验证 token 值与请求中的预期值一致

### 2. 请求体映射
- **商品回调**：请求体应该是 GoodsCallbackItem 数组
- **订单回调**：请求体应该是完整的对象（除了token字段）

### 3. 字段名映射
- 确认 JSON 字段名正确映射到 protobuf 字段名
- 验证 camelCase 到 snake_case 的转换

### 4. 数据类型转换
- 数值类型的正确转换
- 时间戳的处理
- 枚举值的映射

## 常见问题排查

### 1. token 参数为空
- 检查 URL 路径是否正确
- 确认路由注册是否正确

### 2. 请求体解析失败
- 检查 Content-Type 是否为 application/json
- 验证 JSON 格式是否正确
- 确认字段名是否匹配

### 3. 字段映射错误
- 检查 protobuf 定义中的 json_name 标签
- 验证生成的代码是否正确

### 4. 路由不匹配
- 确认 HTTP 方法是否正确（POST）
- 检查 URL 路径是否完全匹配
- 验证路由注册顺序

## 性能测试建议

### 1. 并发测试
```bash
# 使用 ab 进行并发测试
ab -n 1000 -c 10 -T 'application/json' \
   -p goods_callback_data.json \
   'http://localhost:7788/api/open/callback/virtual/goods/notify/test123'
```

### 2. 压力测试
```bash
# 使用 wrk 进行压力测试
wrk -t12 -c400 -d30s \
    -s post_script.lua \
    'http://localhost:7788/api/open/callback/virtual/goods/notify/test123'
```

### 3. 监控指标
- 响应时间
- 吞吐量
- 错误率
- 内存使用
- CPU 使用率

这些测试示例将帮助验证 token 参数的重新定义是否正确工作。
