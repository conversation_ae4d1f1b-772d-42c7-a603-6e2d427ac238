# Token 参数重新定义

基于 [google.api.http 规范](https://blog.csdn.net/xiaojia1100/article/details/79447283) 对项目中的 token 参数进行重新定义。

## 问题分析

### 当前存在的问题

1. **路径模板语法不规范**：使用了 `{token=*}` 而不是标准的 `{token}`
2. **body 字段映射不当**：当 token 在 URL 路径中时，body 使用了 `"*"`，导致字段重复映射
3. **字段映射冲突**：token 字段同时出现在 URL 路径和请求体中

### 参考文档要点

根据 google.api.http 规范：

1. **路径模板语法**：
   - `{var}` 等同于 `{var=*}` - 匹配单个路径段
   - `{var=**}` - 匹配零个或多个路径段（必须是路径的最后部分）
   - 变量模板不能包含其他变量

2. **HTTP 映射规则**：
   - 叶子字段分为三类：
     a. 在 URL 模板中匹配的字段
     b. 由 `body` 覆盖的字段
     c. 其他字段（映射为查询参数）
   - URL 查询参数映射到未在 URL 模板中匹配且不在 body 中的字段
   - HTTP 请求体只能包含 body 指定的字段

3. **body 字段规则**：
   - `body: "*"` - 除了在路径模板中绑定的字段外，所有字段都映射到请求体
   - `body: "field_name"` - 只有指定字段映射到请求体
   - 省略 body - 表示没有 HTTP 请求体

## 修正方案

### 1. 商品回调通知接口

**修正前：**
```protobuf
rpc GoodsCallback(goofish.v1.GoodsCallbackRequest) returns (goofish.v1.GoodsCallbackResponse) {
  option (google.api.http) = {
    post: "/api/open/callback/virtual/goods/notify/{token=*}"
    body: "*"
  };
}
```

**修正后：**
```protobuf
rpc GoodsCallback(goofish.v1.GoodsCallbackRequest) returns (goofish.v1.GoodsCallbackResponse) {
  option (google.api.http) = {
    post: "/api/open/callback/virtual/goods/notify/{token}"
    body: "items"
  };
}
```

**说明：**
- `{token=*}` 改为 `{token}` - 符合标准路径模板语法
- `body: "*"` 改为 `body: "items"` - 避免 token 字段重复映射，只将 items 字段映射到请求体

### 2. 订单回调通知接口

**修正前：**
```protobuf
rpc OrderCallback(goofish.v1.OrderCallbackRequest) returns (goofish.v1.OrderCallbackResponse) {
  option (google.api.http) = {
    post: "/api/open/callback/virtual/order/notify/{token=*}"
    body: "*"
  };
}
```

**修正后：**
```protobuf
rpc OrderCallback(goofish.v1.OrderCallbackRequest) returns (goofish.v1.OrderCallbackResponse) {
  option (google.api.http) = {
    post: "/api/open/callback/virtual/order/notify/{token}"
    body: "*"
  };
}
```

**说明：**
- `{token=*}` 改为 `{token}` - 符合标准路径模板语法
- 保持 `body: "*"` - 因为 OrderCallbackRequest 中除了 token 外的所有字段都需要在请求体中

## HTTP 映射示例

### 商品回调通知

**HTTP 请求：**
```http
POST /api/open/callback/virtual/goods/notify/abc123
Content-Type: application/json

{
  "items": [
    {
      "goods_no": "G001",
      "goods_type": 1,
      "price": 100,
      "stock": 50,
      "status": 1,
      "change_time": 1640995200
    }
  ]
}
```

**映射到 gRPC：**
```protobuf
GoodsCallbackRequest {
  token: "abc123"
  items: [
    {
      goods_no: "G001"
      goods_type: 1
      price: 100
      stock: 50
      status: 1
      change_time: 1640995200
    }
  ]
}
```

### 订单回调通知

**HTTP 请求：**
```http
POST /api/open/callback/virtual/order/notify/xyz789
Content-Type: application/json

{
  "order_type": 1,
  "order_no": "O001",
  "out_order_no": "OUT001",
  "order_status": 2,
  "end_time": 1640995200,
  "card_items": [],
  "remark": "订单完成"
}
```

**映射到 gRPC：**
```protobuf
OrderCallbackRequest {
  token: "xyz789"
  order_type: 1
  order_no: "O001"
  out_order_no: "OUT001"
  order_status: 2
  end_time: 1640995200
  card_items: []
  remark: "订单完成"
}
```

## 验证令牌接口优化建议

当前的 ValidateTokenRequest 定义：

```protobuf
message ValidateTokenRequest {
  string token = 1 [
    json_name = "isValid",  // 这里的 json_name 不合理
    (gnostic.openapi.v3.property) = {
      description: "令牌"
    }
  ];
  ClientType client_type = 2 [
    json_name = "clientType",
    (gnostic.openapi.v3.property) = {
      description: "客戶端類型"
    }
  ];
}
```

**建议修正：**
- `json_name = "isValid"` 应该改为 `json_name = "token"`，因为字段名应该与实际用途一致

## 实施结果

### 已完成的修改

1. **修正了 i_goofish.proto 中的 HTTP 路由定义**：
   - `{token=*}` → `{token}` - 符合标准路径模板语法
   - 商品回调：`body: "*"` → `body: "items"` - 避免字段重复映射
   - 订单回调：保持 `body: "*"` - 符合业务需求

2. **修正了 authentication.proto 中的字段定义**：
   - ValidateTokenRequest.token 的 `json_name` 从 `"isValid"` 改为 `"token"`

3. **重新生成了相关代码**：
   - HTTP 路由代码正确生成
   - 字段绑定逻辑正确实现
   - OpenAPI 文档已更新

### 验证结果

生成的 HTTP 处理代码显示：

1. **商品回调接口**：
   ```go
   // 路由：POST /api/open/callback/virtual/goods/notify/{token}
   ctx.Bind(&in.Items)     // 只绑定 items 到请求体
   ctx.BindVars(&in)       // 绑定 token 到路径参数
   ```

2. **订单回调接口**：
   ```go
   // 路由：POST /api/open/callback/virtual/order/notify/{token}
   ctx.Bind(&in)           // 绑定整个结构体到请求体（除token外）
   ctx.BindVars(&in)       // 绑定 token 到路径参数
   ```

### API 映射示例

**商品回调**：
- URL: `/api/open/callback/virtual/goods/notify/abc123`
- Body: `[{"goodsNo": "G001", ...}]` (直接是items数组)
- 映射: `token="abc123"`, `items=[{...}]`

**订单回调**：
- URL: `/api/open/callback/virtual/order/notify/xyz789`
- Body: `{"orderType": 1, "orderNo": "O001", ...}`
- 映射: `token="xyz789"`, 其他字段从body映射

## 总结

通过以上修正，token 参数的定义现在：

1. **符合 google.api.http 规范** - 使用正确的路径模板语法
2. **避免字段映射冲突** - 确保 token 字段只在 URL 路径中出现
3. **提高 API 一致性** - 统一 token 参数的处理方式
4. **便于客户端集成** - 清晰的字段映射规则
5. **代码生成正确** - HTTP 路由和绑定逻辑符合预期

这些修改使 API 更加规范，便于自动生成客户端代码和文档，同时确保了与 Apifox 文档的一致性。
