# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Kratos Admin API
    description: Kratos Admin API
    contact:
        name: tx7do
        url: https://github.com/tx7do/kratos-admin
        email: <EMAIL>
    license:
        name: MIT License
        url: https://github.com/tx7do/kratos-admin/blob/master/LICENSE
    version: "1.0"
paths:
    /admin/v1/admin_login_logs:
        get:
            tags:
                - AdminLoginLogService
            description: 查询后台登录日志列表
            operationId: AdminLoginLogService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListAdminLoginLogResponse'
    /admin/v1/admin_login_logs/{id}:
        get:
            tags:
                - AdminLoginLogService
            description: 查询后台登录日志详情
            operationId: AdminLoginLogService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AdminLoginLog'
    /admin/v1/admin_operation_logs:
        get:
            tags:
                - AdminOperationLogService
            description: 查询后台操作日志列表
            operationId: AdminOperationLogService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListAdminOperationLogResponse'
    /admin/v1/admin_operation_logs/{id}:
        get:
            tags:
                - AdminOperationLogService
            description: 查询后台操作日志详情
            operationId: AdminOperationLogService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AdminOperationLog'
    /admin/v1/api-resources:
        get:
            tags:
                - ApiResourceService
            description: 查询API资源列表
            operationId: ApiResourceService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListApiResourceResponse'
        post:
            tags:
                - ApiResourceService
            description: 创建API资源
            operationId: ApiResourceService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateApiResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/api-resources/sync:
        post:
            tags:
                - ApiResourceService
            description: 同步API资源
            operationId: ApiResourceService_SyncApiResources
            requestBody:
                content:
                    application/json: {}
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/api-resources/walk-route:
        get:
            tags:
                - ApiResourceService
            description: 查询路由数据
            operationId: ApiResourceService_GetWalkRouteData
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListApiResourceResponse'
    /admin/v1/api-resources/{data.id}:
        put:
            tags:
                - ApiResourceService
            description: 更新API资源
            operationId: ApiResourceService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateApiResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/api-resources/{id}:
        get:
            tags:
                - ApiResourceService
            description: 查询API资源详情
            operationId: ApiResourceService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResource'
        delete:
            tags:
                - ApiResourceService
            description: 删除API资源
            operationId: ApiResourceService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/change_password:
        post:
            tags:
                - AuthenticationService
            description: 修改用户密码
            operationId: AuthenticationService_ChangePassword
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ChangePasswordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/departments:
        get:
            tags:
                - DepartmentService
            description: 查询部门列表
            operationId: DepartmentService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListDepartmentResponse'
        post:
            tags:
                - DepartmentService
            description: 创建部门
            operationId: DepartmentService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateDepartmentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/departments/{data.id}:
        put:
            tags:
                - DepartmentService
            description: 更新部门
            operationId: DepartmentService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateDepartmentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/departments/{id}:
        get:
            tags:
                - DepartmentService
            description: 查询部门详情
            operationId: DepartmentService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Department'
        delete:
            tags:
                - DepartmentService
            description: 删除部门
            operationId: DepartmentService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/dict:
        get:
            tags:
                - DictService
            description: 查询字典列表
            operationId: DictService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListDictResponse'
        post:
            tags:
                - DictService
            description: 创建字典
            operationId: DictService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateDictRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/dict/{data.id}:
        put:
            tags:
                - DictService
            description: 更新字典
            operationId: DictService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateDictRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/dict/{id}:
        get:
            tags:
                - DictService
            description: 查询字典详情
            operationId: DictService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Dict'
        delete:
            tags:
                - DictService
            description: 删除字典
            operationId: DictService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/file:upload:
        put:
            tags:
                - OssService
            description: PUT方法上传文件
            operationId: OssService_PutUploadFile
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UploadOssFileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadOssFileResponse'
        post:
            tags:
                - OssService
            description: POST方法上传文件
            operationId: OssService_PostUploadFile
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UploadOssFileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadOssFileResponse'
    /admin/v1/file:upload-url:
        post:
            tags:
                - OssService
            description: 获取对象存储（OSS）上传用的预签名链接
            operationId: OssService_OssUploadUrl
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/OssUploadUrlRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/OssUploadUrlResponse'
    /admin/v1/files:
        get:
            tags:
                - FileService
            description: 查询文件列表
            operationId: FileService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListFileResponse'
        post:
            tags:
                - FileService
            description: 创建文件
            operationId: FileService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateFileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/files/{data.id}:
        put:
            tags:
                - FileService
            description: 更新文件
            operationId: FileService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateFileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/files/{id}:
        get:
            tags:
                - FileService
            description: 查询文件详情
            operationId: FileService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/File'
        delete:
            tags:
                - FileService
            description: 删除文件
            operationId: FileService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/login:
        post:
            tags:
                - AuthenticationService
            description: 登录
            operationId: AuthenticationService_Login
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LoginRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LoginResponse'
            security:
                - {}
    /admin/v1/login-restrictions:
        get:
            tags:
                - AdminLoginRestrictionService
            description: 查询后台登录限制列表
            operationId: AdminLoginRestrictionService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListAdminLoginRestrictionResponse'
        post:
            tags:
                - AdminLoginRestrictionService
            description: 创建后台登录限制
            operationId: AdminLoginRestrictionService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateAdminLoginRestrictionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/login-restrictions/{data.id}:
        put:
            tags:
                - AdminLoginRestrictionService
            description: 更新后台登录限制
            operationId: AdminLoginRestrictionService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateAdminLoginRestrictionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/login-restrictions/{id}:
        get:
            tags:
                - AdminLoginRestrictionService
            description: 查询后台登录限制详情
            operationId: AdminLoginRestrictionService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AdminLoginRestriction'
        delete:
            tags:
                - AdminLoginRestrictionService
            description: 删除后台登录限制
            operationId: AdminLoginRestrictionService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/logout:
        post:
            tags:
                - AuthenticationService
            description: 登出
            operationId: AuthenticationService_Logout
            requestBody:
                content:
                    application/json: {}
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/me:
        get:
            tags:
                - UserProfileService
            description: 获取用户资料
            operationId: UserProfileService_GetUser
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/User'
        put:
            tags:
                - UserProfileService
            description: 更新用户资料
            operationId: UserProfileService_UpdateUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/menus:
        get:
            tags:
                - MenuService
            description: 查询菜单列表
            operationId: MenuService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListMenuResponse'
        post:
            tags:
                - MenuService
            description: 创建菜单
            operationId: MenuService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateMenuRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/menus/{data.id}:
        put:
            tags:
                - MenuService
            description: 更新菜单
            operationId: MenuService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateMenuRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/menus/{id}:
        get:
            tags:
                - MenuService
            description: 查询菜单详情
            operationId: MenuService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Menu'
        delete:
            tags:
                - MenuService
            description: 删除菜单
            operationId: MenuService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: operatorId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/notifications:
        get:
            tags:
                - NotificationMessageService
            description: 查询通知消息列表
            operationId: NotificationMessageService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListNotificationMessageResponse'
        post:
            tags:
                - NotificationMessageService
            description: 创建通知消息
            operationId: NotificationMessageService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateNotificationMessageRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/notifications/{data.id}:
        put:
            tags:
                - NotificationMessageService
            description: 更新通知消息
            operationId: NotificationMessageService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateNotificationMessageRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/notifications/{id}:
        get:
            tags:
                - NotificationMessageService
            description: 查询通知消息详情
            operationId: NotificationMessageService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/NotificationMessage'
        delete:
            tags:
                - NotificationMessageService
            description: 删除通知消息
            operationId: NotificationMessageService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/notifications:categories:
        get:
            tags:
                - NotificationMessageCategoryService
            description: 查询通知消息分类列表
            operationId: NotificationMessageCategoryService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListNotificationMessageCategoryResponse'
        post:
            tags:
                - NotificationMessageCategoryService
            description: 创建通知消息分类
            operationId: NotificationMessageCategoryService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateNotificationMessageCategoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/notifications:categories/{data.id}:
        put:
            tags:
                - NotificationMessageCategoryService
            description: 更新通知消息分类
            operationId: NotificationMessageCategoryService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateNotificationMessageCategoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/notifications:categories/{id}:
        get:
            tags:
                - NotificationMessageCategoryService
            description: 查询通知消息分类详情
            operationId: NotificationMessageCategoryService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/NotificationMessageCategory'
        delete:
            tags:
                - NotificationMessageCategoryService
            description: 删除通知消息分类
            operationId: NotificationMessageCategoryService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/notifications:recipients:
        get:
            tags:
                - NotificationMessageRecipientService
            description: 查询通知消息接收者列表
            operationId: NotificationMessageRecipientService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListNotificationMessageRecipientResponse'
        post:
            tags:
                - NotificationMessageRecipientService
            description: 创建通知消息接收者
            operationId: NotificationMessageRecipientService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateNotificationMessageRecipientRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/notifications:recipients/{data.id}:
        put:
            tags:
                - NotificationMessageRecipientService
            description: 更新通知消息接收者
            operationId: NotificationMessageRecipientService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateNotificationMessageRecipientRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/notifications:recipients/{id}:
        get:
            tags:
                - NotificationMessageRecipientService
            description: 查询通知消息接收者详情
            operationId: NotificationMessageRecipientService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/NotificationMessageRecipient'
        delete:
            tags:
                - NotificationMessageRecipientService
            description: 删除通知消息接收者
            operationId: NotificationMessageRecipientService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/organizations:
        get:
            tags:
                - OrganizationService
            description: 查询组织列表
            operationId: OrganizationService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListOrganizationResponse'
        post:
            tags:
                - OrganizationService
            description: 创建组织
            operationId: OrganizationService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateOrganizationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/organizations/{data.id}:
        put:
            tags:
                - OrganizationService
            description: 更新组织
            operationId: OrganizationService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateOrganizationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/organizations/{id}:
        get:
            tags:
                - OrganizationService
            description: 查询组织详情
            operationId: OrganizationService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Organization'
        delete:
            tags:
                - OrganizationService
            description: 删除组织
            operationId: OrganizationService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/perm-codes:
        get:
            tags:
                - RouterService
            description: 查询权限码列表
            operationId: RouterService_ListPermissionCode
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListPermissionCodeResponse'
    /admin/v1/positions:
        get:
            tags:
                - PositionService
            description: 查询职位列表
            operationId: PositionService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListPositionResponse'
        post:
            tags:
                - PositionService
            description: 创建职位
            operationId: PositionService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreatePositionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/positions/{data.id}:
        put:
            tags:
                - PositionService
            description: 更新职位
            operationId: PositionService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdatePositionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/positions/{id}:
        get:
            tags:
                - PositionService
            description: 查询职位详情
            operationId: PositionService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Position'
        delete:
            tags:
                - PositionService
            description: 删除职位
            operationId: PositionService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/private_messages:
        get:
            tags:
                - PrivateMessageService
            description: 查询私信消息列表
            operationId: PrivateMessageService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListPrivateMessageResponse'
        post:
            tags:
                - PrivateMessageService
            description: 创建私信消息
            operationId: PrivateMessageService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreatePrivateMessageRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/private_messages/{data.id}:
        put:
            tags:
                - PrivateMessageService
            description: 更新私信消息
            operationId: PrivateMessageService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdatePrivateMessageRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/private_messages/{id}:
        get:
            tags:
                - PrivateMessageService
            description: 查询私信消息详情
            operationId: PrivateMessageService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PrivateMessage'
        delete:
            tags:
                - PrivateMessageService
            description: 删除私信消息
            operationId: PrivateMessageService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/refresh_token:
        post:
            tags:
                - AuthenticationService
            description: 刷新认证令牌
            operationId: AuthenticationService_RefreshToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LoginRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LoginResponse'
    /admin/v1/roles:
        get:
            tags:
                - RoleService
            description: 查询角色列表
            operationId: RoleService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListRoleResponse'
        post:
            tags:
                - RoleService
            description: 创建角色
            operationId: RoleService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateRoleRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/roles/{data.id}:
        put:
            tags:
                - RoleService
            description: 更新角色
            operationId: RoleService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateRoleRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/roles/{id}:
        get:
            tags:
                - RoleService
            description: 查询角色详情
            operationId: RoleService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Role'
        delete:
            tags:
                - RoleService
            description: 删除角色
            operationId: RoleService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/routes:
        get:
            tags:
                - RouterService
            description: 查询路由列表
            operationId: RouterService_ListRoute
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListRouteResponse'
    /admin/v1/tasks:
        get:
            tags:
                - TaskService
            description: 查询调度任务列表
            operationId: TaskService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListTaskResponse'
        post:
            tags:
                - TaskService
            description: 创建调度任务
            operationId: TaskService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/tasks/{data.id}:
        put:
            tags:
                - TaskService
            description: 更新调度任务
            operationId: TaskService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/tasks/{id}:
        get:
            tags:
                - TaskService
            description: 查询调度任务详情
            operationId: TaskService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Task'
        delete:
            tags:
                - TaskService
            description: 删除调度任务
            operationId: TaskService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/tasks:control:
        post:
            tags:
                - TaskService
            description: 控制调度任务
            operationId: TaskService_ControlTask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ControlTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/tasks:restart:
        post:
            tags:
                - TaskService
            description: 重启所有的调度任务
            operationId: TaskService_RestartAllTask
            requestBody:
                content:
                    application/json: {}
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/RestartAllTaskResponse'
    /admin/v1/tasks:stop:
        post:
            tags:
                - TaskService
            description: 停止所有的调度任务
            operationId: TaskService_StopAllTask
            requestBody:
                content:
                    application/json: {}
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/tenants:
        get:
            tags:
                - TenantService
            description: 获取租户列表
            operationId: TenantService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListTenantResponse'
        post:
            tags:
                - TenantService
            description: 创建租户
            operationId: TenantService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateTenantRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/tenants/{data.id}:
        put:
            tags:
                - TenantService
            description: 更新租户
            operationId: TenantService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateTenantRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/tenants/{id}:
        get:
            tags:
                - TenantService
            description: 获取租户数据
            operationId: TenantService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Tenant'
        delete:
            tags:
                - TenantService
            description: 删除租户
            operationId: TenantService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/ueditor:
        get:
            tags:
                - UEditorService
            description: UEditor API
            operationId: UEditorService_UEditorAPI
            parameters:
                - name: action
                  in: query
                  schema:
                    type: string
                - name: encode
                  in: query
                  schema:
                    type: string
                - name: start
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: size
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UEditorResponse'
        post:
            tags:
                - UEditorService
            description: 上传文件
            operationId: UEditorService_UploadFile
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UEditorUploadRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UEditorUploadResponse'
    /admin/v1/users:
        get:
            tags:
                - UserService
            description: 获取用户列表
            operationId: UserService_List
            parameters:
                - name: page
                  in: query
                  description: 当前页码
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  description: 每一页的行数
                  schema:
                    type: integer
                    format: int32
                - name: query
                  in: query
                  description: AND过滤参数，其语法为json格式的字符串，如：{"key1":"val1","key2":"val2"}，具体请参见：https://github.com/tx7do/go-utils/tree/main/entgo/query/README.md
                  schema:
                    type: string
                - name: or
                  in: query
                  description: OR过滤参数，语法同AND过滤参数。
                  schema:
                    type: string
                - name: orderBy
                  in: query
                  description: 排序条件，其语法为JSON字符串，例如：{"val1", "-val2"}。字段名前加'-'为降序，否则为升序。
                  schema:
                    type: array
                    items:
                        type: string
                - name: noPaging
                  in: query
                  description: 是否不分页，如果为true，则page和pageSize参数无效。
                  schema:
                    type: boolean
                - name: fieldMask
                  in: query
                  description: 字段掩码，其作用为SELECT中的字段，其语法为使用逗号分隔字段名，例如：id,realName,userName。如果为空则选中所有字段，即SELECT *。
                  schema:
                    type: string
                    format: field-mask
                - name: tenantId
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListUserResponse'
        post:
            tags:
                - UserService
            description: 创建用户
            operationId: UserService_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/users/{data.id}:
        put:
            tags:
                - UserService
            description: 更新用户
            operationId: UserService_Update
            parameters:
                - name: data.id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/users/{id}:
        get:
            tags:
                - UserService
            description: 获取用户数据
            operationId: UserService_Get
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/User'
        delete:
            tags:
                - UserService
            description: 删除用户
            operationId: UserService_Delete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content: {}
    /api/open/callback/virtual/goods/notify/{token}:
        post:
            tags:
                - GoofishApi
            description: 商品回调通知
            operationId: GoofishApi_GoodsCallback
            parameters:
                - name: token
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsCallbackItem'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsCallbackResponse'
    /api/open/callback/virtual/order/notify/{token}:
        post:
            tags:
                - GoofishApi
            description: 订单回调通知
            operationId: GoofishApi_OrderCallback
            parameters:
                - name: token
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/OrderCallbackRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/OrderCallbackResponse'
    /goofish/goods/change/subscribe:
        post:
            tags:
                - GoofishApi
            description: 订阅商品变更通知
            operationId: GoofishApi_GoodsChangeSubscribe
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsChangeSubscribeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsChangeSubscribeResponse'
    /goofish/goods/change/subscribe/list:
        post:
            tags:
                - GoofishApi
            description: 查询商品订阅列表
            operationId: GoofishApi_GetGoodsChangeSubscribeList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsChangeSubscribeListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsChangeSubscribeListResponse'
    /goofish/goods/change/unsubscribe:
        post:
            tags:
                - GoofishApi
            description: 取消商品变更通知
            operationId: GoofishApi_GoodsChangeUnsubscribe
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsChangeUnsubscribeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsChangeUnsubscribeResponse'
    /goofish/goods/detail:
        post:
            tags:
                - GoofishApi
            description: 查询商品详情
            operationId: GoofishApi_GetGoodsDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsDetailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsDetailResponse'
    /goofish/goods/list:
        post:
            tags:
                - GoofishApi
            description: 查询商品列表
            operationId: GoofishApi_GetGoodsList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsListResponse'
    /goofish/open/info:
        post:
            tags:
                - GoofishApi
            description: 查询平台信息
            operationId: GoofishApi_GetPlatformInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PlatformInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PlatformInfoResponse'
    /goofish/order/detail:
        post:
            tags:
                - GoofishApi
            description: 查询订单详情
            operationId: GoofishApi_GetOrderDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/OrderDetailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/OrderDetailResponse'
    /goofish/order/purchase/create:
        post:
            tags:
                - GoofishApi
            description: 创建卡密订单
            operationId: GoofishApi_CreateCardOrder
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateCardOrderRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateCardOrderResponse'
    /goofish/order/recharge/create:
        post:
            tags:
                - GoofishApi
            description: 创建直充订单
            operationId: GoofishApi_CreateRechargeOrder
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateRechargeOrderRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateRechargeOrderResponse'
    /goofish/user/info:
        post:
            tags:
                - GoofishApi
            description: 查询商户信息
            operationId: GoofishApi_GetUserInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UserInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserInfoResponse'
components:
    schemas:
        AdminLoginLog:
            type: object
            properties:
                id:
                    type: integer
                    description: 后台登录日志ID
                    format: uint32
                loginIp:
                    type: string
                    description: 登录IP地址
                loginMac:
                    type: string
                    description: 登录MAC地址
                loginTime:
                    type: string
                    description: 登录时间
                    format: date-time
                statusCode:
                    type: integer
                    description: 状态码
                    format: int32
                success:
                    type: boolean
                    description: 登录是否成功
                reason:
                    type: string
                    description: 登录失败原因
                location:
                    type: string
                    description: 登录地理位置
                userAgent:
                    type: string
                    description: 浏览器的用户代理信息
                browserName:
                    type: string
                    description: 浏览器名称
                browserVersion:
                    type: string
                    description: 浏览器版本
                clientId:
                    type: string
                    description: 客户端ID
                clientName:
                    type: string
                    description: 客户端名称
                osName:
                    type: string
                    description: 操作系统名称
                osVersion:
                    type: string
                    description: 操作系统版本
                userId:
                    type: integer
                    description: 操作者用户ID
                    format: uint32
                username:
                    type: string
                    description: 操作者账号名
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
            description: 后台登录日志
        AdminLoginRestriction:
            type: object
            properties:
                id:
                    type: integer
                    description: 后台登录限制ID
                    format: uint32
                targetId:
                    type: integer
                    description: 目标用户ID
                    format: uint32
                type:
                    enum:
                        - LOGIN_RESTRICTION_TYPE_UNSPECIFIED
                        - BLACKLIST
                        - WHITELIST
                    type: string
                    description: 限制类型
                    format: enum
                method:
                    enum:
                        - LOGIN_RESTRICTION_METHOD_UNSPECIFIED
                        - IP
                        - MAC
                        - REGION
                        - TIME
                        - DEVICE
                    type: string
                    description: 限制方式
                    format: enum
                value:
                    type: string
                    description: 限制值（如IP地址、MAC地址或地区代码）
                reason:
                    type: string
                    description: 限制原因
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 后台登录限制
        AdminOperationLog:
            type: object
            properties:
                id:
                    type: integer
                    description: 后台操作日志ID
                    format: uint32
                costTime:
                    pattern: ^-?(?:0|[1-9][0-9]{0,11})(?:\.[0-9]{1,9})?s$
                    type: string
                    description: 操作耗时
                success:
                    type: boolean
                    description: 操作是否成功
                requestId:
                    type: string
                    description: 请求ID
                statusCode:
                    type: integer
                    description: 状态码
                    format: int32
                reason:
                    type: string
                    description: 操作失败原因
                location:
                    type: string
                    description: 操作地理位置
                operation:
                    type: string
                    description: 操作方法
                method:
                    type: string
                    description: 请求方法
                path:
                    type: string
                    description: 请求路径
                apiModule:
                    type: string
                    description: API所属模块
                apiDescription:
                    type: string
                    description: API操作描述
                referer:
                    type: string
                    description: 请求源
                requestUri:
                    type: string
                    description: 请求URI
                requestHeader:
                    type: string
                    description: 请求头
                requestBody:
                    type: string
                    description: 请求体
                response:
                    type: string
                    description: 响应信息
                userId:
                    type: integer
                    description: 操作者用户ID
                    format: uint32
                username:
                    type: string
                    description: 操作者账号名
                clientIp:
                    type: string
                    description: 操作者IP
                userAgent:
                    type: string
                    description: 浏览器的用户代理信息
                browserName:
                    type: string
                    description: 浏览器名称
                browserVersion:
                    type: string
                    description: 浏览器版本
                clientId:
                    type: string
                    description: 客户端ID
                clientName:
                    type: string
                    description: 客户端名称
                osName:
                    type: string
                    description: 操作系统名称
                osVersion:
                    type: string
                    description: 操作系统版本
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
            description: 后台操作日志
        ApiResource:
            type: object
            properties:
                id:
                    type: integer
                    description: 资源ID
                    format: uint32
                operation:
                    type: string
                    description: 接口操作名
                path:
                    type: string
                    description: 接口路径
                method:
                    type: string
                    description: 请求方法（GET/POST/PUT/DELETE）
                module:
                    type: string
                    description: 所属业务模块（如 “用户管理”“支付系统”）
                moduleDescription:
                    type: string
                    description: 模块描述
                description:
                    type: string
                    description: 描述
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: API资源
        BizContent:
            type: object
            properties:
                account:
                    type: string
                gameName:
                    type: string
                gameRole:
                    type: string
                gameArea:
                    type: string
                gameServer:
                    type: string
                buyerIp:
                    type: string
                buyerArea:
                    type: string
            description: 订单相关
        CardItem:
            type: object
            properties:
                cardNo:
                    type: string
                cardPwd:
                    type: string
        CardOrderData:
            type: object
            properties:
                orderNo:
                    type: string
                outOrderNo:
                    type: string
                orderStatus:
                    type: integer
                    format: int32
                orderAmount:
                    type: string
                orderTime:
                    type: string
                endTime:
                    type: string
                cardItems:
                    type: array
                    items:
                        $ref: '#/components/schemas/CardItem'
                remark:
                    type: string
        ChangePasswordRequest:
            type: object
            properties:
                username:
                    type: string
                    description: 用户名
                oldPassword:
                    type: string
                    description: 旧密码
                newPassword:
                    type: string
                    description: 新密码
            description: 修改用户密码 - 请求
        ControlTaskRequest:
            type: object
            properties:
                controlType:
                    enum:
                        - ControlType_Start
                        - ControlType_Stop
                        - ControlType_Restart
                    type: string
                    format: enum
                typeName:
                    type: string
                    description: 任务执行类型名，例如 "send_email"、"generate_report" 等，用于区分不同类型的任务
            description: 控制调度任务 - 请求
        CreateAdminLoginRestrictionRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/AdminLoginRestriction'
            description: 创建后台登录限制 - 请求
        CreateApiResourceRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/ApiResource'
            description: 创建 - 请求
        CreateCardOrderRequest:
            type: object
            properties:
                orderNo:
                    type: string
                goodsNo:
                    type: string
                buyQuantity:
                    type: integer
                    format: int32
                maxAmount:
                    type: string
                notifyUrl:
                    type: string
                bizOrderNo:
                    type: string
            description: 创建卡密订单
        CreateCardOrderResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/CardOrderData'
        CreateDepartmentRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Department'
            description: 创建部门 - 请求
        CreateDictRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Dict'
            description: 创建字典 - 请求
        CreateFileRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/File'
            description: 创建 - 请求
        CreateMenuRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Menu'
            description: 创建菜单 - 请求
        CreateNotificationMessageCategoryRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/NotificationMessageCategory'
            description: 创建通知消息分类 - 请求
        CreateNotificationMessageRecipientRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/NotificationMessageRecipient'
            description: 创建通知消息接收者 - 请求
        CreateNotificationMessageRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/NotificationMessage'
            description: 创建通知消息 - 请求
        CreateOrganizationRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Organization'
            description: 创建组织 - 请求
        CreatePositionRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Position'
            description: 创建职位 - 请求
        CreatePrivateMessageRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/PrivateMessage'
            description: 创建私信消息 - 请求
        CreateRechargeOrderRequest:
            type: object
            properties:
                orderNo:
                    type: string
                goodsNo:
                    type: string
                bizContent:
                    $ref: '#/components/schemas/BizContent'
                buyQuantity:
                    type: integer
                    format: int32
                maxAmount:
                    type: string
                notifyUrl:
                    type: string
                bizOrderNo:
                    type: string
            description: 创建直充订单
        CreateRechargeOrderResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/RechargeOrderData'
        CreateRoleRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Role'
            description: 创建角色 - 请求
        CreateTaskRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Task'
            description: 创建调度任务 - 请求
        CreateTenantRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Tenant'
            description: 创建租户 - 请求
        CreateUserRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/User'
                password:
                    readOnly: true
                    type: string
                    description: 用户登录密码
            description: 创建用户 - 请求
        Department:
            type: object
            properties:
                id:
                    type: integer
                    description: 部门ID
                    format: uint32
                name:
                    type: string
                    description: 部门名称
                organizationId:
                    type: integer
                    description: 所属组织ID
                    format: int32
                organizationName:
                    type: string
                    description: 所属组织名称
                sortId:
                    type: integer
                    description: 排序编号
                    format: int32
                status:
                    enum:
                        - ON
                        - OFF
                    type: string
                    default: ON
                    description: 状态
                remark:
                    type: string
                    description: 备注
                parentId:
                    type: integer
                    description: 父节点ID
                    format: uint32
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/Department'
                    description: 子节点树
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 部门
        Dict:
            type: object
            properties:
                id:
                    type: integer
                    description: 字典ID
                    format: uint32
                category:
                    type: string
                    description: 字典分类
                categoryDesc:
                    type: string
                    description: 字典分类名称
                key:
                    type: string
                    description: 字典键
                value:
                    type: string
                    description: 字典值
                valueDesc:
                    type: string
                    description: 字典值名称
                valueDataType:
                    type: string
                    description: 字典值数据类型
                status:
                    enum:
                        - ON
                        - OFF
                    type: string
                    default: ON
                    description: 字典状态
                sortId:
                    type: integer
                    description: 排序编号
                    format: int32
                remark:
                    type: string
                    description: 备注
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 字典
        File:
            type: object
            properties:
                id:
                    type: integer
                    description: 文件ID
                    format: uint32
                provider:
                    enum:
                        - MINIO
                        - ALIYUN
                        - AWS
                        - AZURE
                        - BAIDU
                        - QINIU
                        - TENCENT
                        - GOOGLE
                        - HUAWEI
                        - QCLOUD
                        - LOCAL
                    type: string
                    description: OSS供应商
                    format: enum
                bucketName:
                    type: string
                    description: 存储桶名称
                fileDirectory:
                    type: string
                    description: 文件目录
                fileGuid:
                    type: string
                    description: 文件Guid
                saveFileName:
                    type: string
                    description: 保存文件名
                fileName:
                    type: string
                    description: 文件名
                extension:
                    type: string
                    description: 文件扩展名
                size:
                    type: string
                    description: 文件字节长度
                sizeFormat:
                    type: string
                    description: 文件大小格式化
                linkUrl:
                    type: string
                    description: 链接地址
                md5:
                    type: string
                    description: md5码，防止上传重复文件
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 文件
        GoodsCallbackItem:
            type: object
            properties:
                goodsNo:
                    type: string
                goodsType:
                    type: integer
                    format: int32
                price:
                    type: string
                stock:
                    type: integer
                    format: int32
                status:
                    type: integer
                    format: int32
                changeTime:
                    type: string
            description: 商品回调通知
        GoodsCallbackResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        GoodsChangeSubscribeListData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoodsChangeSubscribeListItem'
                count:
                    type: integer
                    format: int32
        GoodsChangeSubscribeListItem:
            type: object
            properties:
                goodsType:
                    type: integer
                    format: int32
                goodsNo:
                    type: string
                subscribeTime:
                    type: string
                token:
                    type: string
                notifyUrl:
                    type: string
        GoodsChangeSubscribeListRequest:
            type: object
            properties:
                goodsType:
                    type: integer
                    format: int32
                goodsNo:
                    type: string
                pageNo:
                    type: integer
                    format: int32
                pageSize:
                    type: integer
                    format: int32
            description: 查询商品订阅列表
        GoodsChangeSubscribeListResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/GoodsChangeSubscribeListData'
        GoodsChangeSubscribeRequest:
            type: object
            properties:
                goodsType:
                    type: integer
                    format: int32
                goodsNo:
                    type: string
                token:
                    type: string
                notifyUrl:
                    type: string
            description: 订阅商品变更通知
        GoodsChangeSubscribeResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        GoodsChangeUnsubscribeRequest:
            type: object
            properties:
                goodsType:
                    type: integer
                    format: int32
                goodsNo:
                    type: string
                token:
                    type: string
            description: 取消商品变更通知
        GoodsChangeUnsubscribeResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        GoodsDetail:
            type: object
            properties:
                goodsNo:
                    type: string
                goodsType:
                    type: integer
                    format: int32
                goodsName:
                    type: string
                price:
                    type: string
                stock:
                    type: integer
                    format: int32
                status:
                    type: integer
                    format: int32
                updateTime:
                    type: string
                template:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoodsTemplate'
            description: 商品详情
        GoodsDetailRequest:
            type: object
            properties:
                goodsType:
                    type: integer
                    format: int32
                goodsNo:
                    type: string
            description: 查询商品详情
        GoodsDetailResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/GoodsDetail'
        GoodsListData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoodsDetail'
                count:
                    type: integer
                    format: int32
        GoodsListRequest:
            type: object
            properties:
                keyword:
                    type: string
                goodsType:
                    type: integer
                    format: int32
                pageNo:
                    type: integer
                    format: int32
                pageSize:
                    type: integer
                    format: int32
            description: 查询商品列表
        GoodsListResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/GoodsListData'
        GoodsTemplate:
            type: object
            properties:
                code:
                    type: string
                name:
                    type: string
                desc:
                    type: string
                check:
                    type: integer
                    format: int32
            description: 商品详情模板
        KratosStatus:
            type: object
            properties:
                code:
                    type: number
                    description: 错误码
                    format: int32
                message:
                    type: string
                    description: 错误消息
                reason:
                    type: string
                    description: 错误原因
                metadata:
                    type: object
                    description: 元数据
            description: Kratos错误返回
        ListAdminLoginLogResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/AdminLoginLog'
                total:
                    type: integer
                    format: uint32
            description: 查询后台登录日志列表 - 回应
        ListAdminLoginRestrictionResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/AdminLoginRestriction'
                total:
                    type: integer
                    format: uint32
            description: 查询后台登录限制列表 - 回应
        ListAdminOperationLogResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/AdminOperationLog'
                total:
                    type: integer
                    format: uint32
            description: 查询后台操作日志列表 - 回应
        ListApiResourceResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/ApiResource'
                total:
                    type: integer
                    format: uint32
            description: 查询列表 - 回应
        ListDepartmentResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/Department'
                total:
                    type: integer
                    format: uint32
            description: 部门列表 - 答复
        ListDictResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/Dict'
                total:
                    type: integer
                    format: uint32
            description: 查询字典列表 - 回应
        ListFileResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/File'
                total:
                    type: integer
                    format: uint32
            description: 查询列表 - 回应
        ListMenuResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/Menu'
                total:
                    type: integer
                    format: uint32
            description: 查询菜单列表 - 回应
        ListNotificationMessageCategoryResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/NotificationMessageCategory'
                total:
                    type: integer
                    format: uint32
            description: 查询通知消息分类列表 - 回应
        ListNotificationMessageRecipientResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/NotificationMessageRecipient'
                total:
                    type: integer
                    format: uint32
            description: 查询通知消息接收者列表 - 回应
        ListNotificationMessageResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/NotificationMessage'
                total:
                    type: integer
                    format: uint32
            description: 查询通知消息列表 - 回应
        ListOrganizationResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/Organization'
                total:
                    type: integer
                    format: uint32
            description: 组织列表 - 答复
        ListPermissionCodeResponse:
            type: object
            properties:
                codes:
                    type: array
                    items:
                        type: string
            description: 查询权限码列表 - 回应
        ListPositionResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/Position'
                total:
                    type: integer
                    format: uint32
            description: 获取职位列表 - 答复
        ListPrivateMessageResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/PrivateMessage'
                total:
                    type: integer
                    format: uint32
            description: 查询私信消息列表 - 回应
        ListRoleResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/Role'
                total:
                    type: integer
                    format: uint32
            description: 角色列表 - 答复
        ListRouteResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/RouteItem'
            description: 查询路由列表 - 回应
        ListTaskResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/Task'
                total:
                    type: integer
                    format: uint32
            description: 查询调度任务列表 - 回应
        ListTenantResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/Tenant'
                total:
                    type: integer
                    format: uint32
            description: 租户列表 - 答复
        ListUserResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/User'
                total:
                    type: integer
                    format: uint32
            description: 获取用户列表 - 答复
        LoginRequest:
            required:
                - grant_type
            type: object
            properties:
                grant_type:
                    type: string
                    default: password
                    description: 授权类型，此处的值固定为"password"，必选项。
                client_id:
                    type: string
                    description: 客户端ID
                client_secret:
                    type: string
                    description: 客户端密钥
                scope:
                    type: string
                    description: 以空格分隔的用户授予范围列表。如果未提供，scope则授权任何范围，默认为空列表。
                redirect_uri:
                    type: string
                    description: 跳转链接
                username:
                    type: string
                password:
                    type: string
                refresh_token:
                    type: string
                    description: 更新令牌，用来获取下一次的访问令牌，可选项。如果访问令牌将过期，则返回刷新令牌很有用，应用程序可以使用该刷新令牌来获取另一个访问令牌。但是，通过隐式授予颁发的令牌不能颁发刷新令牌。
                code:
                    type: string
                    description: 授权请求中收到的一次性验证/认证码。(当使用授权码模式时)
            description: 用户后台登录 - 请求
        LoginResponse:
            type: object
            properties:
                access_token:
                    type: string
                    description: 访问令牌，必选项。授权服务器颁发的访问令牌字符串。
                refresh_token:
                    type: string
                    description: 更新令牌，用来获取下一次的访问令牌，可选项。如果访问令牌将过期，则返回刷新令牌很有用，应用程序可以使用该刷新令牌来获取另一个访问令牌。但是，通过隐式授予颁发的令牌不能颁发刷新令牌。
                token_type:
                    type: string
                    default: Bearer
                    description: 令牌的类型，该值大小写不敏感，必选项，可以是bearer类型或mac类型，通常只是字符串“Bearer”。
                expires_in:
                    type: string
                    description: 令牌有效时间，单位为秒。如果访问令牌过期，服务器应回复授予访问令牌的持续时间。如果省略该参数，必须其他方式设置过期时间。
                scope:
                    type: string
                    description: 以空格分隔的用户授予范围列表。如果未提供，scope则授权任何范围，默认为空列表。
            description: 用户后台登录 - 回应
        Menu:
            type: object
            properties:
                id:
                    type: integer
                    description: 菜单ID
                    format: int32
                status:
                    enum:
                        - OFF
                        - ON
                        - ON
                        - OFF
                    type: string
                    default: ON
                    description: 菜单状态
                    format: enum
                type:
                    enum:
                        - FOLDER
                        - MENU
                        - BUTTON
                    type: string
                    default: FOLDER
                    description: 菜单类型
                    format: enum
                path:
                    type: string
                    description: 路由路径
                redirect:
                    type: string
                    description: 重定向地址
                alias:
                    type: string
                    description: 路由别名
                name:
                    type: string
                    description: 路由命名，然后我们可以使用 name 而不是 path 来传递 to 属性给 <router-link>。
                component:
                    type: string
                    description: 指向的组件
                meta:
                    $ref: '#/components/schemas/RouteMeta'
                parentId:
                    type: integer
                    description: 父节点ID
                    format: int32
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/Menu'
                    description: 子节点树
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 菜单
        NotificationMessage:
            type: object
            properties:
                id:
                    type: integer
                    description: 消息ID
                    format: uint32
                subject:
                    type: string
                    description: 主题
                content:
                    type: string
                    description: 内容
                status:
                    enum:
                        - MessageStatus_Unknown
                        - DRAFT
                        - PUBLISHED
                        - SCHEDULED
                        - REVOKED
                        - ARCHIVED
                        - DELETED
                        - SENT
                        - RECEIVED
                        - READ
                    type: string
                    description: 消息状态
                    format: enum
                categoryId:
                    type: integer
                    description: 分类ID
                    format: uint32
                categoryName:
                    type: string
                    description: 分类名称
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 通知消息
        NotificationMessageCategory:
            type: object
            properties:
                id:
                    type: integer
                    description: 分类ID
                    format: uint32
                name:
                    type: string
                    description: 名称
                code:
                    type: string
                    description: 编码
                sortId:
                    type: integer
                    description: 排序编号
                    format: int32
                enable:
                    type: boolean
                    description: 是否启用
                parentId:
                    type: integer
                    description: 父节点ID
                    format: uint32
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/NotificationMessageCategory'
                    description: 子节点树
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 通知消息分类
        NotificationMessageRecipient:
            type: object
            properties:
                id:
                    type: integer
                    description: 记录ID
                    format: uint32
                messageId:
                    type: integer
                    description: 群发消息ID
                    format: uint32
                recipientId:
                    type: integer
                    description: 接收者用户ID
                    format: uint32
                status:
                    enum:
                        - MessageStatus_Unknown
                        - DRAFT
                        - PUBLISHED
                        - SCHEDULED
                        - REVOKED
                        - ARCHIVED
                        - DELETED
                        - SENT
                        - RECEIVED
                        - READ
                    type: string
                    description: 消息状态
                    format: enum
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 通知消息接收者
        OrderCallbackRequest:
            type: object
            properties:
                token:
                    type: string
                orderType:
                    type: integer
                    format: int32
                orderNo:
                    type: string
                outOrderNo:
                    type: string
                orderStatus:
                    type: integer
                    format: int32
                endTime:
                    type: string
                cardItems:
                    type: array
                    items:
                        $ref: '#/components/schemas/CardItem'
                remark:
                    type: string
            description: 订单回调通知
        OrderCallbackResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        OrderDetailData:
            type: object
            properties:
                orderType:
                    type: integer
                    format: int32
                orderNo:
                    type: string
                outOrderNo:
                    type: string
                orderStatus:
                    type: integer
                    format: int32
                orderAmount:
                    type: string
                goodsNo:
                    type: string
                goodsName:
                    type: string
                buyQuantity:
                    type: integer
                    format: int32
                orderTime:
                    type: string
                endTime:
                    type: string
                bizContent:
                    $ref: '#/components/schemas/BizContent'
                cardItems:
                    type: array
                    items:
                        $ref: '#/components/schemas/CardItem'
                remark:
                    type: string
        OrderDetailRequest:
            type: object
            properties:
                orderType:
                    type: integer
                    format: int32
                orderNo:
                    type: string
                outOrderNo:
                    type: string
            description: 查询订单详情
        OrderDetailResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/OrderDetailData'
        Organization:
            type: object
            properties:
                id:
                    type: integer
                    description: 组织ID
                    format: uint32
                name:
                    type: string
                    description: 组织名称
                sortId:
                    type: integer
                    description: 排序编号
                    format: int32
                status:
                    enum:
                        - ON
                        - OFF
                    type: string
                    default: ON
                    description: 状态
                remark:
                    type: string
                    description: 备注
                parentId:
                    type: integer
                    description: 父节点ID
                    format: uint32
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/Organization'
                    description: 子节点树
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 组织
        OssUploadUrlRequest:
            type: object
            properties:
                method:
                    enum:
                        - Put
                        - Post
                    type: string
                    description: 上传文件所用的HTTP方法，支持POST和PUT
                    format: enum
                contentType:
                    type: string
                    description: 文件的MIME类型
                bucketName:
                    type: string
                    description: 文件桶名称，如果不填写，将会根据文件名或者MIME类型进行自动解析
                filePath:
                    type: string
                    description: 远端的文件路径，可以不填写
                fileName:
                    type: string
                    description: 文件名，如果不填写，则会生成UUID，有同名文件也会改为UUID
            description: 获取对象存储上传链接 - 请求
        OssUploadUrlResponse:
            type: object
            properties:
                uploadUrl:
                    type: string
                    description: 文件的上传链接，默认1个小时的过期时间
                downloadUrl:
                    type: string
                    description: 文件的下载链接
                bucketName:
                    type: string
                    description: 文件桶名称
                objectName:
                    type: string
                    description: 文件名
                formData:
                    type: object
                    additionalProperties:
                        type: string
                    description: 表单数据，使用POST方法时填写
            description: 获取对象存储上传链接 - 回应
        PlatformInfoData:
            type: object
            properties:
                appId:
                    type: string
        PlatformInfoRequest:
            type: object
            properties: {}
            description: 查询平台信息
        PlatformInfoResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/PlatformInfoData'
        Position:
            type: object
            properties:
                id:
                    type: integer
                    description: 职位ID
                    format: uint32
                name:
                    type: string
                    description: 职位名称
                sortId:
                    type: integer
                    description: 排序编号
                    format: int32
                code:
                    type: string
                    description: 职位值
                status:
                    enum:
                        - ON
                        - OFF
                    type: string
                    default: ON
                    description: 状态
                remark:
                    type: string
                    description: 备注
                parentId:
                    type: integer
                    description: 父节点ID
                    format: uint32
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/Position'
                    description: 子节点树
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 职位
        PrivateMessage:
            type: object
            properties:
                id:
                    type: integer
                    description: 消息ID
                    format: uint32
                subject:
                    type: string
                    description: 主题
                content:
                    type: string
                    description: 内容
                status:
                    enum:
                        - MessageStatus_Unknown
                        - DRAFT
                        - PUBLISHED
                        - SCHEDULED
                        - REVOKED
                        - ARCHIVED
                        - DELETED
                        - SENT
                        - RECEIVED
                        - READ
                    type: string
                    description: 消息状态
                    format: enum
                senderId:
                    type: integer
                    description: 发送者用户ID
                    format: uint32
                senderName:
                    type: string
                    description: 发送者用户名称
                senderAvatar:
                    type: string
                    description: 发送者用户头像
                receiverId:
                    type: integer
                    description: 接收者用户ID
                    format: uint32
                receiverName:
                    type: string
                    description: 接收者用户名称
                receiverAvatar:
                    type: string
                    description: 接收者用户头像
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 私信消息
        RechargeOrderData:
            type: object
            properties:
                orderNo:
                    type: string
                outOrderNo:
                    type: string
                orderStatus:
                    type: integer
                    format: int32
                orderAmount:
                    type: string
                goodsName:
                    type: string
                orderTime:
                    type: string
                endTime:
                    type: string
                remark:
                    type: string
        RestartAllTaskResponse:
            type: object
            properties:
                count:
                    type: integer
                    format: int32
            description: 重启调度任务 - 回应
        Role:
            type: object
            properties:
                id:
                    type: integer
                    description: 角色ID
                    format: uint32
                name:
                    type: string
                    description: 角色名称
                sortId:
                    type: integer
                    description: 排序编号
                    format: int32
                code:
                    type: string
                    description: 角色值
                status:
                    enum:
                        - ON
                        - OFF
                    type: string
                    default: ON
                    description: 状态
                remark:
                    type: string
                    description: 备注
                menus:
                    type: array
                    items:
                        type: integer
                        format: uint32
                    description: 分配的菜单列表
                apis:
                    type: array
                    items:
                        type: integer
                        format: uint32
                    description: 分配的API列表
                parentId:
                    type: integer
                    description: 父节点ID
                    format: uint32
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/Role'
                    description: 子节点树
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 角色
        RouteItem:
            type: object
            properties:
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/RouteItem'
                    description: 子节点树
                path:
                    type: string
                    description: 路由路径
                redirect:
                    type: string
                    description: 重定向地址
                alias:
                    type: string
                    description: 路由别名
                name:
                    type: string
                    description: 路由命名，然后我们可以使用 name 而不是 path 来传递 to 属性给 <router-link>。
                component:
                    type: string
                    description: 指向的组件
                meta:
                    $ref: '#/components/schemas/RouteMeta'
            description: 路由项
        RouteMeta:
            type: object
            properties:
                activeIcon:
                    type: string
                    description: 激活图标，用于：菜单、tab
                activePath:
                    type: string
                    description: 当前激活的菜单，有时候不想激活现有菜单，需要激活父级菜单时使用
                affixTab:
                    type: boolean
                    description: 是否固定标签页
                affixTabOrder:
                    type: integer
                    description: 固定标签页的顺序
                    format: int32
                authority:
                    type: array
                    items:
                        type: string
                    description: 权限列表，需要特定的角色标识才可以访问
                badge:
                    type: string
                    description: 徽标
                badgeType:
                    enum:
                        - dot
                        - normal
                    type: string
                    description: 徽标类型
                badgeVariants:
                    enum:
                        - default
                        - destructive
                        - primary
                        - success
                        - warning
                    type: string
                    description: 徽标颜色
                hideChildrenInMenu:
                    type: boolean
                    description: 当前路由的子级在菜单中不展现
                hideInBreadcrumb:
                    type: boolean
                    description: 当前路由在面包屑中不展现
                hideInMenu:
                    type: boolean
                    description: 当前路由在菜单中不展现
                hideInTab:
                    type: boolean
                    description: 当前路由在标签页不展现
                icon:
                    type: string
                    description: 图标，用于：菜单、标签页
                iframeSrc:
                    type: string
                    description: iframe 地址
                ignoreAccess:
                    type: boolean
                    description: 忽略权限，直接可以访问
                keepAlive:
                    type: boolean
                    description: 开启KeepAlive缓存
                link:
                    type: string
                    description: 外链-跳转路径
                loaded:
                    type: boolean
                    description: 路由是否已经加载过
                maxNumOfOpenTab:
                    type: integer
                    description: 标签页最大打开数量
                    format: int32
                menuVisibleWithForbidden:
                    type: boolean
                    description: 菜单可以看到，但是访问会被重定向到403
                openInNewWindow:
                    type: boolean
                    description: 在新窗口打开
                order:
                    type: integer
                    description: 排序编号，用于路由->菜单排序
                    format: int32
                title:
                    type: string
                    description: 标题名称，路由上显示的标题
            description: 路由元数据
        Task:
            type: object
            properties:
                id:
                    type: integer
                    description: 任务ID
                    format: uint32
                type:
                    enum:
                        - PERIODIC
                        - DELAY
                        - WAIT_RESULT
                    type: string
                    description: 任务类型
                    format: enum
                typeName:
                    type: string
                    description: 任务执行类型名，例如 "send_email"、"generate_report" 等，用于区分不同类型的任务
                taskPayload:
                    type: string
                    description: 任务数据，以 JSON 格式存储，方便存储不同类型和数量的参数
                cronSpec:
                    type: string
                    description: cron表达式，用于定义任务的调度时间
                taskOptions:
                    $ref: '#/components/schemas/TaskOption'
                enable:
                    type: boolean
                    description: 启用/禁用任务
                remark:
                    type: string
                    description: 备注
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 调度任务
        TaskOption:
            type: object
            properties:
                retryCount:
                    type: integer
                    description: 任务最多可以重试的次数
                    format: uint32
                timeout:
                    pattern: ^-?(?:0|[1-9][0-9]{0,11})(?:\.[0-9]{1,9})?s$
                    type: string
                    description: 任务超时时间
                deadline:
                    type: string
                    description: 任务截止时间
                    format: date-time
                processIn:
                    pattern: ^-?(?:0|[1-9][0-9]{0,11})(?:\.[0-9]{1,9})?s$
                    type: string
                    description: 任务延迟处理时间
                processAt:
                    type: string
                    description: 任务执行时间点
                    format: date-time
            description: 任务选项
        Tenant:
            type: object
            properties:
                id:
                    type: integer
                    description: 租户ID
                    format: uint32
                name:
                    type: string
                    description: 租户名称
                code:
                    type: string
                    description: 租户编码
                memberCount:
                    type: integer
                    description: 成员数量
                    format: int32
                status:
                    enum:
                        - ON
                        - OFF
                    type: string
                    default: ON
                    description: 状态
                remark:
                    type: string
                    description: 备注
                subscriptionAt:
                    type: string
                    description: 订阅时间
                    format: date-time
                unsubscribeAt:
                    type: string
                    description: 退订时间
                    format: date-time
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 租户
        UEditorResponse:
            type: object
            properties:
                imageActionName:
                    type: string
                imageFieldName:
                    type: string
                imageMaxSize:
                    type: string
                imageAllowFiles:
                    type: array
                    items:
                        type: string
                imageCompressEnable:
                    type: boolean
                imageCompressBorder:
                    type: string
                imageInsertAlign:
                    type: string
                imageUrlPrefix:
                    type: string
                imagePathFormat:
                    type: string
                scrawlActionName:
                    type: string
                scrawlFieldName:
                    type: string
                scrawlMaxSize:
                    type: string
                scrawlUrlPrefix:
                    type: string
                scrawlInsertAlign:
                    type: string
                scrawlPathFormat:
                    type: string
                snapscreenActionName:
                    type: string
                snapscreenUrlPrefix:
                    type: string
                snapscreenInsertAlign:
                    type: string
                snapscreenPathFormat:
                    type: string
                catcherActionName:
                    type: string
                catcherFieldName:
                    type: string
                catcherLocalDomain:
                    type: array
                    items:
                        type: string
                catcherUrlPrefix:
                    type: string
                catcherMaxSize:
                    type: string
                catcherAllowFiles:
                    type: array
                    items:
                        type: string
                catcherPathFormat:
                    type: string
                videoActionName:
                    type: string
                videoFieldName:
                    type: string
                videoUrlPrefix:
                    type: string
                videoMaxSize:
                    type: string
                videoAllowFiles:
                    type: array
                    items:
                        type: string
                videoPathFormat:
                    type: string
                fileActionName:
                    type: string
                fileFieldName:
                    type: string
                fileUrlPrefix:
                    type: string
                fileMaxSize:
                    type: string
                fileAllowFiles:
                    type: array
                    items:
                        type: string
                filePathFormat:
                    type: string
                imageManagerActionName:
                    type: string
                imageManagerListSize:
                    type: string
                imageManagerUrlPrefix:
                    type: string
                imageManagerInsertAlign:
                    type: string
                imageManagerAllowFiles:
                    type: array
                    items:
                        type: string
                imageManagerListPath:
                    type: string
                fileManagerActionName:
                    type: string
                fileManagerUrlPrefix:
                    type: string
                fileManagerListSize:
                    type: string
                fileManagerAllowFiles:
                    type: array
                    items:
                        type: string
                FileManagerListPath:
                    type: string
                formulaConfig:
                    $ref: '#/components/schemas/UEditorResponse_FormulaConfig'
                state:
                    type: string
                start:
                    type: integer
                    format: int32
                total:
                    type: integer
                    format: int32
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/UEditorResponse_Item'
        UEditorResponse_FormulaConfig:
            type: object
            properties:
                imageUrlTemplate:
                    type: string
        UEditorResponse_Item:
            type: object
            properties:
                url:
                    type: string
                mtime:
                    type: string
        UEditorUploadRequest:
            type: object
            properties:
                action:
                    type: string
                    description: 动作
                file:
                    type: string
                    description: 文件内容
                    format: bytes
                sourceFileName:
                    type: string
                    description: 原文件文件名
                mime:
                    type: string
                    description: 文件的MIME类型
        UEditorUploadResponse:
            type: object
            properties:
                state:
                    type: string
                url:
                    type: string
                title:
                    type: string
                original:
                    type: string
                type:
                    type: string
                size:
                    type: integer
                    format: int32
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/UEditorUploadResponse_Item'
        UEditorUploadResponse_Item:
            type: object
            properties:
                state:
                    type: string
                url:
                    type: string
                title:
                    type: string
                original:
                    type: string
                type:
                    type: string
                size:
                    type: integer
                    format: int32
        UpdateAdminLoginRestrictionRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/AdminLoginRestriction'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新后台登录限制 - 请求
        UpdateApiResourceRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/ApiResource'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新 - 请求
        UpdateDepartmentRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Department'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新部门 - 请求
        UpdateDictRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Dict'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新字典 - 请求
        UpdateFileRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/File'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新 - 请求
        UpdateMenuRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Menu'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新菜单 - 请求
        UpdateNotificationMessageCategoryRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/NotificationMessageCategory'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新通知消息分类 - 请求
        UpdateNotificationMessageRecipientRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/NotificationMessageRecipient'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新通知消息接收者 - 请求
        UpdateNotificationMessageRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/NotificationMessage'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新通知消息 - 请求
        UpdateOrganizationRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Organization'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新组织 - 请求
        UpdatePositionRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Position'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新职位 - 请求
        UpdatePrivateMessageRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/PrivateMessage'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新私信消息 - 请求
        UpdateRoleRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Role'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新角色 - 请求
        UpdateTaskRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Task'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新调度任务 - 请求
        UpdateTenantRequest:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/Tenant'
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新租户 -请求
        UpdateUserRequest:
            required:
                - data
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/User'
                password:
                    readOnly: true
                    type: string
                    description: 用户登录密码
                updateMask:
                    example: id,realname,username
                    type: string
                    description: 要更新的字段列表
                    format: field-mask
                allowMissing:
                    type: boolean
                    description: 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
            description: 更新用户 - 请求
        UploadOssFileRequest:
            type: object
            properties:
                bucketName:
                    type: string
                    description: 文件桶名称
                objectName:
                    type: string
                    description: 文件名
                file:
                    type: string
                    description: 文件内容
                    format: bytes
                sourceFileName:
                    type: string
                    description: 原文件文件名
                mime:
                    type: string
                    description: 文件的MIME类型
        UploadOssFileResponse:
            type: object
            properties:
                url:
                    type: string
        User:
            type: object
            properties:
                id:
                    type: integer
                    description: 用户ID
                    format: uint32
                workId:
                    type: integer
                    description: 工号
                    format: uint32
                orgId:
                    type: integer
                    description: 部门ID
                    format: uint32
                positionId:
                    type: integer
                    description: 岗位ID
                    format: uint32
                tenantId:
                    type: integer
                    description: 租户ID
                    format: uint32
                username:
                    type: string
                    description: 登录名
                nickname:
                    type: string
                    description: 昵称
                realname:
                    type: string
                    description: 真实姓名
                avatar:
                    type: string
                    description: 头像
                email:
                    type: string
                    description: 邮箱
                mobile:
                    type: string
                    description: 手机号
                telephone:
                    type: string
                    description: 座机号
                gender:
                    enum:
                        - SECRET
                        - MALE
                        - FEMALE
                    type: string
                    description: 性别
                    format: enum
                address:
                    type: string
                    description: 住址
                region:
                    type: string
                    description: 国家地区
                description:
                    type: string
                    description: 个人描述
                remark:
                    type: string
                    description: 备注名
                lastLoginTime:
                    type: string
                    description: 最后登录时间
                    format: date-time
                lastLoginIp:
                    type: string
                    description: 最后登录IP
                status:
                    enum:
                        - OFF
                        - ON
                        - ON
                        - OFF
                    type: string
                    default: ON
                    description: 用户状态
                    format: enum
                authority:
                    enum:
                        - GUEST
                        - CUSTOMER_USER
                        - TENANT_ADMIN
                        - SYS_ADMIN
                    type: string
                    default: CUSTOMER_USER
                    description: 权限
                    format: enum
                roles:
                    type: array
                    items:
                        type: string
                    description: 角色码列表
                createBy:
                    type: integer
                    description: 创建者ID
                    format: uint32
                updateBy:
                    type: integer
                    description: 更新者ID
                    format: uint32
                createTime:
                    type: string
                    description: 创建时间
                    format: date-time
                updateTime:
                    type: string
                    description: 更新时间
                    format: date-time
                deleteTime:
                    type: string
                    description: 删除时间
                    format: date-time
            description: 用户
        UserInfoData:
            type: object
            properties:
                balance:
                    type: string
        UserInfoRequest:
            type: object
            properties: {}
            description: 查询商户信息
        UserInfoResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/UserInfoData'
    responses:
        default:
            description: default kratos response
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/KratosStatus'
    securitySchemes:
        OAuth2PasswordBearer:
            type: oauth2
            flows:
                password:
                    tokenUrl: /admin/v1/login
                    refreshUrl: /admin/v1/refresh_token
                    scopes: {}
security:
    - OAuth2PasswordBearer: []
tags:
    - name: AdminLoginLogService
      description: 后台登录日志管理服务
    - name: AdminLoginRestrictionService
      description: 后台登录限制管理服务
    - name: AdminOperationLogService
      description: 后台操作日志管理服务
    - name: ApiResourceService
      description: API资源管理服务
    - name: AuthenticationService
      description: 用户后台登录认证服务
    - name: DepartmentService
      description: 部门管理服务
    - name: DictService
      description: 字典管理服务
    - name: FileService
      description: 文件管理服务
    - name: GoofishApi
      description: goofish API 路由配置，字段与 Apifox 文档保持一致
    - name: MenuService
      description: 后台菜单管理服务
    - name: NotificationMessageCategoryService
      description: 通知消息分类管理服务
    - name: NotificationMessageRecipientService
      description: 通知消息接收者管理服务
    - name: NotificationMessageService
      description: 通知消息管理服务
    - name: OrganizationService
      description: 组织管理服务
    - name: OssService
      description: OSS服务
    - name: PositionService
      description: 职位管理服务
    - name: PrivateMessageService
      description: 私信消息管理服务
    - name: RoleService
      description: 角色管理服务
    - name: RouterService
      description: 网站后台动态路由服务
    - name: TaskService
      description: 调度任务管理服务
    - name: TenantService
      description: 租户管理服务
    - name: UEditorService
      description: UEditor后端服务
    - name: UserProfileService
      description: 用户个人资料服务
    - name: UserService
      description: 用户管理服务
